const df = require("durable-functions");

module.exports = async function (context, req) {
  const client = df.getClient(context);
  const instanceId = req.params.instanceId;
  
  if (!instanceId) {
    context.res = {
      status: 400,
      body: { error: "Instance ID is required" }
    };
    return;
  }
  
  try {
    // Get the status of the orchestration
    const status = await client.getStatus(instanceId);
    
    if (!status) {
      context.res = {
        status: 404,
        body: { error: "Orchestration instance not found" }
      };
      return;
    }
    
    context.res = {
      status: 200,
      body: {
        instanceId: status.instanceId,
        runtimeStatus: status.runtimeStatus,
        input: status.input,
        output: status.output,
        createdTime: status.createdTime,
        lastUpdatedTime: status.lastUpdatedTime,
        customStatus: status.customStatus
      },
      headers: {
        "Content-Type": "application/json"
      }
    };
  } catch (error) {
    context.log.error("Error getting orchestration status:", error);
    context.res = {
      status: 500,
      body: { error: "Failed to get orchestration status" }
    };
  }
};
