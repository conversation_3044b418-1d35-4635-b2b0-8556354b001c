const df = require("durable-functions");

module.exports = df.orchestrator(function* (context) {
  const input = context.df.getInput();
  const startTime = new Date();

  try {
    // Create activity function calls to run in parallel
    const tasks = [
      context.df.callActivity("durable-func-one", input),
      context.df.callActivity("durable-func-two", input),
      context.df.callActivity("durable-func-three", input),
    ];

    // Execute all functions in parallel using Task.all()
    const results = yield context.df.Task.all(tasks);

    const endTime = new Date();
    const totalExecutionTime = endTime - startTime;

    return {
      message: "All three functions executed in parallel successfully!",
      totalExecutionTime: `${totalExecutionTime}ms`,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      functionsExecuted: 3,
      results: results,
      summary: {
        "durable-func-one": results[0].executionTime || "N/A",
        "durable-func-two": results[1].executionTime || "N/A",
        "durable-func-three": results[2].executionTime || "N/A",
        "parallel-total": `${totalExecutionTime}ms`,
      },
    };
  } catch (error) {
    context.log.error("Error executing parallel functions:", error);
    throw new Error(
      `Failed to execute functions in parallel: ${error.message}`
    );
  }
});

// HTTP Trigger to start the orchestrator
module.exports.httpStart = async function (context, req) {
  const client = df.getClient(context);
  const input = req.body || req.query || {};

  const instanceId = await client.startNew(
    "orchestrator-function",
    undefined,
    input
  );

  context.log(`Started orchestration with ID = '${instanceId}'.`);

  return client.createCheckStatusResponse(context.bindingData.req, instanceId);
};
