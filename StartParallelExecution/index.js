const df = require("durable-functions");

module.exports = async function (context, req) {
  const client = df.getClient(context);
  
  // Get input from request body or query parameters
  const input = req.body || req.query || {};
  
  // Start the parallel orchestration
  const instanceId = await client.startNew("ParallelOrchestrator", undefined, input);
  
  context.log(`Started parallel orchestration with ID = '${instanceId}'.`);
  
  // Return the management URLs for the orchestration
  const response = client.createCheckStatusResponse(context.bindingData.req, instanceId);
  
  context.res = {
    status: 202,
    body: {
      message: "Parallel execution of durable functions started successfully",
      instanceId: instanceId,
      statusQueryGetUri: response.statusQueryGetUri,
      sendEventPostUri: response.sendEventPostUri,
      terminatePostUri: response.terminatePostUri,
      purgeHistoryDeleteUri: response.purgeHistoryDeleteUri,
      restartPostUri: response.restartPostUri
    },
    headers: {
      "Content-Type": "application/json"
    }
  };
};
