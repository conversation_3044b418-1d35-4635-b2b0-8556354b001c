const df = require("durable-functions");

module.exports = df.orchestrator(function* (context) {
  const input = context.df.getInput();
  
  // Create tasks for all three functions to run in parallel
  const tasks = [];
  
  // Add all three durable functions as parallel tasks
  tasks.push(context.df.callActivity("durable-func-one", input));
  tasks.push(context.df.callActivity("durable-func-two", input));
  tasks.push(context.df.callActivity("durable-func-three", input));
  
  // Wait for all tasks to complete in parallel
  const results = yield context.df.Task.all(tasks);
  
  // Return combined results with execution summary
  return {
    message: "All three durable functions executed in parallel",
    totalFunctions: 3,
    results: results,
    executionSummary: {
      startTime: context.df.currentUtcDateTime,
      functionsExecuted: results.map(r => r.functionName),
      totalExecutionTime: "Parallel execution - longest function determines total time"
    }
  };
});
